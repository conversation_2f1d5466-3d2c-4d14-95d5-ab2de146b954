-- Función para calcular días de vacaciones basado en antigüedad
CREATE OR REPLACE FUNCTION fn_calcular_dias_vacaciones(fecha_ingreso_param DATE)
RETURNS INTEGER AS $$
DECLARE
    anos_antiguedad INTEGER;
    dias_vacaciones INTEGER;
BEGIN
    -- Calcular años de antigüedad
    anos_antiguedad := EXTRACT(YEAR FROM AGE(CURRENT_DATE, fecha_ingreso_param));
    
    -- Calcular días de vacaciones según las reglas
    CASE 
        WHEN anos_antiguedad = 0 THEN dias_vacaciones := 0;
        WHEN anos_antiguedad = 1 THEN dias_vacaciones := 12;
        WHEN anos_antiguedad = 2 THEN dias_vacaciones := 14;
        WHEN anos_antiguedad = 3 THEN dias_vacaciones := 16;
        WHEN anos_antiguedad = 4 THEN dias_vacaciones := 18;
        WHEN anos_antiguedad = 5 THEN dias_vacaciones := 20;
        WHEN anos_antiguedad >= 6 THEN dias_vacaciones := 22;
        ELSE dias_vacaciones := 0;
    END CASE;
    
    RETURN dias_vacaciones;
END;
$$ LANGUAGE plpgsql;
