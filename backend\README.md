# Backend API - Sistema de Empleados Coppel

Backend desarrollado en Node.js + Express + PostgreSQL para el sistema de gestión de empleados.

## Características

- **CRUD completo** de empleados
- **Cálculos automáticos** de vacaciones y aguinaldo
- **Funciones personalizadas** en PostgreSQL
- **API RESTful** con validaciones
- **Manejo de errores** robusto
- **CORS configurado** para Angular

## Endpoints Disponibles

### Empleados Básicos
- `POST /api/empleados` - Crear nuevo empleado
- `PUT /api/empleados/:id` - Actualizar empleado
- `DELETE /api/empleados/:id` - Eliminar empleado

### Empleados con Detalles (Vacaciones y Aguinaldo)
- `GET /api/empleados/detalles/todos` - Obtener todos con cálculos

## Instalación y Configuración

### 1. Instalar dependencias
```bash
npm install
```

### 2. Configurar variables de entorno
```bash
cp .env.example .env
```

Editar `.env` con tus datos de PostgreSQL:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=empleados_coppel
DB_USER=postgres
DB_PASSWORD=tu_password
PORT=3000
NODE_ENV=development
```

### 3. Ejecutar el servidor
```bash
# Desarrollo (con nodemon)
npm run dev

# Producción
npm start
```

## Funciones PostgreSQL Utilizadas

El backend utiliza las siguientes funciones de PostgreSQL:

- `fn_crear_empleado()` - Crear empleado
- `fn_actualizar_empleado()` - Actualizar empleado
- `fn_eliminar_empleado()` - Eliminar empleado
- `fn_obtener_todos_empleados_detalles()` - Todos con cálculos