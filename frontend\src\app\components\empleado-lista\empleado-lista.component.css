.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.header h2 {
  color: #333;
  margin: 0;
  font-size: 1.8rem;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
}

.form-container {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  align-items: end;
}

.form-grid input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-buttons {
  display: flex;
  gap: 10px;
}

/* Tabla de empleados */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #ddd;
}

.empleados-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.empleados-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.empleados-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.empleados-table tr:hover {
  background: #f8f9fa;
}

.empleados-table tr.highlight {
  background: #e3f2fd;
}

.actions-cell {
  text-align: center;
  white-space: nowrap;
}

/* Botones */
.btn-primary, .btn-success, .btn-secondary, .btn-edit, .btn-delete {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary {
  background: #007bff;
  color: white;
}
.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-success {
  background: #28a745;
  color: white;
}
.btn-success:hover {
  background: #1e7e34;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}
.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-1px);
}

.btn-edit {
  background: #ffc107;
  color: #212529;
  padding: 6px 10px;
  margin-right: 5px;
}
.btn-edit:hover {
  background: #e0a800;
  transform: translateY(-1px);
}

.btn-delete {
  background: #dc3545;
  color: white;
  padding: 6px 10px;
}
.btn-delete:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.btn-delete:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Mensajes */
.mensaje {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
  animation: slideIn 0.3s ease;
}

.mensaje.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.mensaje.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.loading, .no-empleados, .no-results {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading {
  font-size: 18px;
}

.no-empleados p, .no-results p {
  font-size: 18px;
  margin-bottom: 20px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .actions {
    flex-direction: column;
    gap: 10px;
  }

  .search-input {
    width: 100%;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .table-container {
    overflow-x: auto;
  }

  .empleados-table {
    min-width: 800px;
  }

  .empleados-table th,
  .empleados-table td {
    padding: 8px 6px;
    font-size: 13px;
  }

  .btn-edit, .btn-delete {
    padding: 4px 8px;
    font-size: 12px;
  }
}
