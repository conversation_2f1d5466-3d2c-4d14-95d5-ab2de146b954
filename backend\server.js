const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Importar configuración y rutas
const empleadoRoutes = require('./routes/empleadoRoutes');
const { errorHandler, notFound } = require('./middleware/errorHandler');

// Crear aplicación Express
const app = express();
const PORT = process.env.PORT || 3000;

// Middlewares de seguridad y logging
app.use(helmet());
app.use(morgan('combined'));

// Configurar CORS para permitir peticiones desde Angular
app.use(cors({
  origin: ['http://localhost:4200', 'http://127.0.0.1:4200'], // URLs del frontend Angular
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Middleware para parsear JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Ruta de salud del servidor
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Servidor funcionando correctamente',
    timestamp: new Date().toISOString()
  });
});

// Rutas principales
app.use('/api/empleados', empleadoRoutes);

// Ruta raíz
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'API de Empleados Coppel',
    version: '1.0.0',
    endpoints: {
      empleados: '/api/empleados',
      health: '/health'
    }
  });
});

// Middlewares de manejo de errores (deben ir al final)
app.use(notFound);
app.use(errorHandler);

// Función para iniciar el servidor
const startServer = async () => {
  try {
    // Iniciar servidor
    app.listen(PORT, () => {
      console.log(`Servidor corriendo en puerto ${PORT}`);
      console.log(`URL: http://localhost:${PORT}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
      console.log(`API Empleados: http://localhost:${PORT}/api/empleados`);
      console.log(`Entorno: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('Error al iniciar el servidor:', error.message);
    process.exit(1);
  }
};

// Manejo de errores no capturados
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

// Iniciar servidor
startServer();

module.exports = app;
