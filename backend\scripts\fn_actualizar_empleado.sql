-- FUNCTION: public.fn_actualizar_empleado(integer, character varying, character varying, character varying, numeric, date)

-- DROP FUNCTION IF EXISTS public.fn_actualizar_empleado(integer, character varying, character varying, character varying, numeric, date);

CREATE OR REPLACE FUNCTION public.fn_actualizar_empleado(
	empleado_id integer,
	nombre_param character varying,
	apellido_paterno_param character varying,
	apellido_materno_param character varying,
	sueldo_base_param numeric,
	fecha_ingreso_param date)
    RETURNS TABLE(
        id integer, 
        nombre character varying, 
        apellido_paterno character varying, 
        apellido_materno character varying, 
        sueldo_base numeric, fecha_ingreso date, 
        mensaje character varying) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
BEGIN
    UPDATE empleados 
    SET nombre = nombre_param,
        apellido_paterno = apellido_paterno_param,
        apellido_materno = apellido_materno_param,
        sueldo_base = sueldo_base_param,
        fecha_ingreso = fecha_ingreso_param
    WHERE empleados.id = empleado_id;
    
    IF FOUND THEN
        RETURN QUERY
        SELECT e.id as empleado_id, e.nombre, e.apellido_paterno, e.apellido_materno, e.sueldo_base, e.fecha_ingreso,
            'Empleado actualizado exitosamente'::VARCHAR(100) as mensaje
        FROM empleados e
        WHERE e.id = empleado_id;
    ELSE
        RETURN QUERY
        SELECT NULL::INTEGER, NULL::VARCHAR(100), NULL::VARCHAR(100), NULL::VARCHAR(100), 
            NULL::NUMERIC(10,2), NULL::DATE, 'Empleado no encontrado'::VARCHAR(100);
    END IF;
END;
$BODY$;

ALTER FUNCTION public.fn_actualizar_empleado(integer, character varying, character varying, character varying, numeric, date)
    OWNER TO postgres;
