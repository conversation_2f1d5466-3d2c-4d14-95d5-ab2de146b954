const { pool } = require('../config/database');

class EmpleadoModel {
  
  // Crear empleado
  static async crear(empleadoData) {
    const { nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso } = empleadoData;
    
    try {
      const query = 'SELECT * FROM fn_crear_empleado($1, $2, $3, $4, $5)';
      const values = [nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso];
      
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Error al crear empleado: ${error.message}`);
    }
  }

  // Actualizar empleado
  static async actualizar(id, empleadoData) {
    const { nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso } = empleadoData;
    
    try {
      const query = 'SELECT * FROM fn_actualizar_empleado($1, $2, $3, $4, $5, $6)';
      const values = [id, nombre, apellido_paterno, apellido_materno, sueldo_base, fecha_ingreso];
      
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Error al actualizar empleado: ${error.message}`);
    }
  }

  // Eliminar empleado
  static async eliminar(id) {
    try {
      const query = 'SELECT * FROM fn_eliminar_empleado($1)';
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw new Error(`Error al eliminar empleado: ${error.message}`);
    }
  }

  // Obtener todos los empleados con detalles
  static async obtenerTodosConDetalles() {
    try {
      const query = 'SELECT * FROM fn_obtener_todos_empleados_detalles()';
      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      throw new Error(`Error al obtener empleados con detalles: ${error.message}`);
    }
  }
}

module.exports = EmpleadoModel;
