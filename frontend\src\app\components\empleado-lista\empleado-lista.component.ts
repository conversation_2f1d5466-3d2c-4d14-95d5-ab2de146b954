import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmpleadoService, EmpleadoDetalle } from '../../services/empleado.service';

@Component({
  selector: 'app-empleado-lista',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './empleado-lista.component.html',
  styleUrls: ['./empleado-lista.component.css']
})
export class EmpleadoListaComponent implements OnInit {
  empleados: EmpleadoDetalle[] = [];
  empleadosFiltrados: EmpleadoDetalle[] = [];
  mostrarFormulario = false;
  empleadoEditando: EmpleadoDetalle | null = null;
  cargando = false;
  eliminando: number | null = null;
  mensaje = '';
  mensajeExito = true;
  filtro = '';

  empleadoForm = {
    nombre: '',
    apellido_paterno: '',
    apellido_materno: '',
    sueldo_base: 0,
    fecha_ingreso: ''
  };

  constructor(
    private empleadoService: EmpleadoService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.cargarEmpleados();
  }

  cargarEmpleados() {
    this.cargando = true;
    this.cdr.detectChanges(); // Forzar actualización de la vista

    this.empleadoService.obtenerTodosConDetalles().subscribe({
      next: (response) => {
        this.empleados = response.data || [];
        this.empleadosFiltrados = [...this.empleados];
        this.cargando = false;
        this.cdr.detectChanges(); // Forzar actualización después de cargar
        console.log('Empleados cargados:', this.empleados.length);
      },
      error: (error) => {
        console.error('Error al cargar empleados:', error);
        this.mostrarMensaje('Error al cargar empleados', false);
        this.cargando = false;
        this.cdr.detectChanges();
      }
    });
  }

  guardarEmpleado() {
    if (this.empleadoEditando) {
      // Actualizar empleado existente
      this.empleadoService.actualizarEmpleado(this.empleadoEditando.id!, this.empleadoForm).subscribe({
        next: () => {
          this.mostrarMensaje('Empleado actualizado correctamente', true);
          this.cancelarFormulario();
          this.cargarEmpleados(); // Recargar después de cancelar
        },
        error: (error) => {
          const mensaje = error.error?.message || 'Error al actualizar empleado';
          this.mostrarMensaje('Error al actualizar empleado', false);
          alert(mensaje);
        }
      });
    } else {
      // Crear nuevo empleado
      this.empleadoService.crearEmpleado(this.empleadoForm).subscribe({
        next: () => {
          this.mostrarMensaje('Empleado creado correctamente', true);
          this.cancelarFormulario();
          this.cargarEmpleados(); // Recargar después de cancelar
        },
        error: (error) => {
          const mensaje = error.error?.message || 'Error al crear empleado';
          this.mostrarMensaje('Error al crear empleado', false);
          alert(mensaje);
        }
      });
    }
  }

  eliminarEmpleado(id: number) {
    if (confirm('¿Estás seguro de que quieres eliminar este empleado?')) {
      this.eliminando = id;
      this.cdr.detectChanges(); // Actualizar estado del botón

      this.empleadoService.eliminarEmpleado(id).subscribe({
        next: () => {
          this.mostrarMensaje('Empleado eliminado correctamente', true);
          this.eliminando = null;
          this.cargarEmpleados(); // Recargar lista
        },
        error: () => {
          this.mostrarMensaje('Error al eliminar empleado', false);
          this.eliminando = null;
          this.cdr.detectChanges();
        }
      });
    }
  }

  editarEmpleado(empleado: EmpleadoDetalle) {
    this.empleadoEditando = empleado;
    this.empleadoForm = {
      nombre: empleado.nombre,
      apellido_paterno: empleado.apellido_paterno,
      apellido_materno: empleado.apellido_materno,
      sueldo_base: empleado.sueldo_base,
      fecha_ingreso: empleado.fecha_ingreso.split('T')[0] // Formato para input date
    };
    this.mostrarFormulario = true;
  }

  cancelarFormulario() {
    this.mostrarFormulario = false;
    this.empleadoEditando = null;
    this.empleadoForm = {
      nombre: '',
      apellido_paterno: '',
      apellido_materno: '',
      sueldo_base: 0,
      fecha_ingreso: ''
    };
  }

  // Filtrar empleados
  filtrarEmpleados() {
    if (!this.filtro.trim()) {
      this.empleadosFiltrados = [...this.empleados];
    } else {
      const filtroLower = this.filtro.toLowerCase();
      this.empleadosFiltrados = this.empleados.filter(empleado =>
        empleado.nombre.toLowerCase().includes(filtroLower) ||
        empleado.apellido_paterno.toLowerCase().includes(filtroLower) ||
        empleado.apellido_materno.toLowerCase().includes(filtroLower) ||
        (empleado.id?.toString() || '').includes(filtroLower)
      );
    }
    this.cdr.detectChanges(); // Forzar actualización de la vista
  }

  // Limpiar filtro
  limpiarFiltro() {
    this.filtro = '';
    this.filtrarEmpleados();
  }

  // Mostrar mensaje
  mostrarMensaje(mensaje: string, exito: boolean) {
    this.mensaje = mensaje;
    this.mensajeExito = exito;
    setTimeout(() => {
      this.mensaje = '';
    }, 5000);
  }
}
