const express = require('express');
const EmpleadoController = require('../controllers/empleadoController');

const router = express.Router();

// Rutas para empleados
router.get('/detalles/todos', EmpleadoController.obtenerTodosConDetalles);  // GET /empleados/detalles/todos
router.post('/', EmpleadoController.crear);                                 // POST /empleados
router.put('/:id', EmpleadoController.actualizar);                          // PUT /empleados/:id
router.delete('/:id', EmpleadoController.eliminar);                         // DELETE /empleados/:id

module.exports = router;
