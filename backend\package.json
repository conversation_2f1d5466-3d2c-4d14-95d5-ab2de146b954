{"name": "empleados-coppel-backend", "version": "1.0.0", "description": "Backend API para sistema de empleados Coppel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["empleados", "coppel", "api", "postgresql"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}}